from aiogram import Router, F
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from ..keyboards.shop import get_shop_menu_kb, get_exchange_points_kb, get_back_to_shop_kb, get_bonus_catalog_kb

router = Router()

class ShopStates(StatesGroup):
    main = State()
    exchange = State()
    catalog = State()
    my_bonuses = State()

@router.callback_query(F.data == "shop")
async def show_shop_menu(callback: CallbackQuery, state: FSMContext):
    """Показать меню магазина"""
    # В реальном приложении эти данные будут загружаться из базы данных
    points = 870
    coins = 210
    
    await callback.message.edit_text(
        "🎉 Добро пожаловать в магазин!\n"
        "Здесь ты можешь обменять баллы на монеты и потратить их на бонусы.\n"
        "💰 У тебя сейчас:\n"
        f"Баллы: {points}\n"
        f"Монеты: {coins}",
        reply_markup=get_shop_menu_kb()
    )
    await state.set_state(ShopStates.main)

@router.callback_query(ShopStates.main, F.data == "exchange_points")
async def show_exchange_options(callback: CallbackQuery, state: FSMContext):
    """Показать варианты обмена баллов на монеты"""
    await callback.message.edit_text(
        "Выбери, сколько баллов хочешь обменять на монеты:",
        reply_markup=get_exchange_points_kb()
    )
    await state.set_state(ShopStates.exchange)

@router.callback_query(ShopStates.exchange, F.data.startswith("exchange_"))
async def process_exchange(callback: CallbackQuery, state: FSMContext):
    """Обработать обмен баллов на монеты"""
    exchange_amount = int(callback.data.replace("exchange_", ""))
    
    # В реальном приложении здесь будет логика обновления баланса в базе данных
    # Для примера используем фиксированные значения
    old_points = 870
    old_coins = 210
    
    new_points = old_points - exchange_amount
    new_coins = old_coins + exchange_amount
    
    await callback.message.edit_text(
        "✅ Успешно!\n"
        f"Списано: {exchange_amount} баллов\n"
        f"Начислено: {exchange_amount} монет\n"
        f"💼 Новый баланс: {new_points} баллов | {new_coins} монет",
        reply_markup=get_back_to_shop_kb()
    )

@router.callback_query(ShopStates.main, F.data == "bonus_catalog")
async def show_bonus_catalog(callback: CallbackQuery, state: FSMContext):
    """Показать каталог бонусов"""
    await callback.message.edit_text(
        "🛒 Каталог бонусов\n\n"
        "Выберите бонус, который хотите приобрести за монеты:\n\n"
        "🧪 Бонусные тесты - дополнительные тесты для практики\n"
        "📝 Бонусные задания - полезные материалы и упражнения",
        reply_markup=get_bonus_catalog_kb()
    )
    await state.set_state(ShopStates.catalog)

@router.callback_query(ShopStates.main, F.data == "my_bonuses")
async def show_my_bonuses(callback: CallbackQuery, state: FSMContext):
    """Показать мои бонусы"""
    # В реальном приложении здесь будет запрос к базе данных для получения купленных бонусов
    # Пример купленных бонусов
    purchased_bonuses = [
        {"type": "task", "id": "task_1", "name": "Дополнительные упражнения", "description": "Набор дополнительных упражнений для закрепления материала по теме 'Алканы'."},
        {"type": "test", "id": "test_1", "name": "Бонусный тест по алканам", "description": "Дополнительный тест для проверки знаний."}
    ]

    if not purchased_bonuses:
        await callback.message.edit_text(
            "📦 Мои бонусы\n\n"
            "У вас пока нет приобретенных бонусов.\n"
            "Посетите каталог бонусов, чтобы купить полезные материалы!",
            reply_markup=get_back_to_shop_kb()
        )
    else:
        # Формируем клавиатуру с купленными бонусами
        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
        buttons = []

        for bonus in purchased_bonuses:
            icon = "📝" if bonus["type"] == "task" else "🧪"
            buttons.append([InlineKeyboardButton(
                text=f"{icon} {bonus['name']}",
                callback_data=f"view_bonus_{bonus['type']}_{bonus['id']}"
            )])

        buttons.extend(get_back_to_shop_kb().inline_keyboard)
        bonuses_kb = InlineKeyboardMarkup(inline_keyboard=buttons)

        await callback.message.edit_text(
            "📦 Мои бонусы\n\n"
            "Выберите бонус для просмотра:",
            reply_markup=bonuses_kb
        )

    await state.set_state(ShopStates.my_bonuses)

# Обработчик просмотра купленных бонусных заданий
@router.callback_query(ShopStates.my_bonuses, F.data.startswith("view_bonus_task_"))
async def view_bonus_task(callback: CallbackQuery, state: FSMContext):
    """Просмотр купленного бонусного задания"""
    task_id = callback.data.replace("view_bonus_task_", "")

    # В реальном приложении здесь будет запрос к базе данных
    bonus_tasks = {
        "task_1": {
            "name": "Дополнительные упражнения",
            "description": "Набор дополнительных упражнений для закрепления материала по теме 'Алканы'. Включает 15 задач разного уровня сложности с подробными решениями.\n\n📚 Содержание:\n• Задачи на номенклатуру алканов\n• Упражнения на изомерию\n• Практические задания по свойствам\n• Задачи на горение алканов\n• Контрольные вопросы с ответами"
        }
    }

    task = bonus_tasks.get(task_id)
    if task:
        await callback.message.edit_text(
            f"📝 {task['name']}\n\n"
            f"{task['description']}\n\n"
            "✅ Этот материал доступен вам навсегда!",
            reply_markup=get_back_to_shop_kb()
        )
    else:
        await callback.message.edit_text(
            "❌ Задание не найдено.",
            reply_markup=get_back_to_shop_kb()
        )

# Обработчики покупки бонусных заданий
@router.callback_query(ShopStates.catalog, F.data.startswith("buy_bonus_task_"))
async def buy_bonus_task(callback: CallbackQuery, state: FSMContext):
    """Покупка бонусного задания"""
    task_id = callback.data.replace("buy_bonus_task_", "")

    # В реальном приложении здесь будет запрос к базе данных
    bonus_tasks = {
        "task_1": {"name": "Дополнительные упражнения", "price": 50, "description": "Набор дополнительных упражнений для закрепления материала по теме 'Алканы'. Включает 15 задач разного уровня сложности с подробными решениями."},
        "task_2": {"name": "Конспект по теме", "price": 80, "description": "Подробный конспект по теме 'Изомерия органических соединений'. Содержит схемы, примеры и алгоритмы решения задач."},
        "task_3": {"name": "Практические задачи", "price": 100, "description": "Сборник практических задач повышенной сложности по органической химии. 20 задач с пошаговыми решениями и объяснениями."}
    }

    task = bonus_tasks.get(task_id)
    if not task:
        await callback.message.edit_text(
            "❌ Задание не найдено.",
            reply_markup=get_back_to_shop_kb()
        )
        return

    # Проверяем баланс (в реальном приложении из БД)
    current_coins = 210  # Пример

    if current_coins < task["price"]:
        await callback.message.edit_text(
            f"❌ Недостаточно монет!\n\n"
            f"📝 {task['name']}\n"
            f"💰 Цена: {task['price']} монет\n"
            f"💳 У вас: {current_coins} монет\n\n"
            f"Не хватает: {task['price'] - current_coins} монет",
            reply_markup=get_back_to_shop_kb()
        )
        return

    # Показываем подтверждение покупки
    from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
    confirm_kb = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="✅ Купить", callback_data=f"confirm_buy_task_{task_id}")],
        [InlineKeyboardButton(text="❌ Отмена", callback_data="bonus_catalog")],
        *get_back_to_shop_kb().inline_keyboard
    ])

    await callback.message.edit_text(
        f"📝 {task['name']}\n\n"
        f"📄 Описание:\n{task['description']}\n\n"
        f"💰 Цена: {task['price']} монет\n"
        f"💳 У вас: {current_coins} монет\n\n"
        "Подтвердите покупку:",
        reply_markup=confirm_kb
    )

@router.callback_query(F.data.startswith("confirm_buy_task_"))
async def confirm_buy_bonus_task(callback: CallbackQuery, state: FSMContext):
    """Подтверждение покупки бонусного задания"""
    task_id = callback.data.replace("confirm_buy_task_", "")

    # В реальном приложении здесь будет:
    # 1. Списание монет с баланса пользователя
    # 2. Добавление задания в "Мои бонусы"
    # 3. Запись в историю покупок

    bonus_tasks = {
        "task_1": {"name": "Дополнительные упражнения", "price": 50},
        "task_2": {"name": "Конспект по теме", "price": 80},
        "task_3": {"name": "Практические задачи", "price": 100}
    }

    task = bonus_tasks.get(task_id)
    if task:
        await callback.message.edit_text(
            f"✅ Покупка успешна!\n\n"
            f"📝 Приобретено: {task['name']}\n"
            f"💰 Списано: {task['price']} монет\n\n"
            f"Задание добавлено в раздел 'Мои бонусы'.\n"
            f"Вы можете найти его в главном меню магазина.",
            reply_markup=get_back_to_shop_kb()
        )
    else:
        await callback.message.edit_text(
            "❌ Ошибка при покупке.",
            reply_markup=get_back_to_shop_kb()
        )

@router.callback_query(F.data == "back_to_shop")
async def back_to_shop(callback: CallbackQuery, state: FSMContext):
    """Вернуться в меню магазина"""
    await show_shop_menu(callback, state)